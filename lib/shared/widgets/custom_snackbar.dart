import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_icons.dart';
import 'package:towasl/core/theme/app_text_styles.dart';

class NewSnackBar {
  static ScaffoldFeatureController<SnackBar, SnackBarClosedReason> newSnackbar(
    BuildContext context,
    String title,
    String message,
    String type, {
    Color? colorText,
    Duration? duration = const Duration(seconds: 3),
    bool instantInit = true,
    Widget? titleText,
    Widget? messageText,
    Widget? icon,
    EdgeInsets? margin,
    EdgeInsets? padding,
    double? borderRadius,
    Color? backgroundColor,
    VoidCallback? onTap,
    bool? isDismissible,
  }) {
    // Create content based on type
    Widget content;

    if (type == "2") {
      // Type 2: Show both title and message
      content = Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title.isNotEmpty)
            Container(
              margin: const EdgeInsets.only(right: 10),
              child: titleText ??
                  Text(
                    title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: styleGreyNormal,
                  ),
            ),
          Container(
            margin: const EdgeInsets.only(right: 10),
            child: messageText ??
                Text(
                  message,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: stylePrimaryNormal,
                ),
          ),
        ],
      );
    } else {
      // Default type: Show only title/message
      content = Container(
        margin: const EdgeInsets.only(right: 10),
        child: messageText ??
            Text(
              title,
              style: stylePrimaryNormal,
            ),
      );
    }

    final snackBar = SnackBar(
      content: Row(
        children: [
          if (icon != null) ...[
            icon,
            const SizedBox(width: 10),
          ],
          Expanded(child: content),
          TextButton(
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
            child: SvgPicture.asset(
              AppIcons.msgCloseIcon,
              width: 24,
              height: 24,
            ),
          ),
        ],
      ),
      duration: duration!,
      backgroundColor: backgroundColor ?? AppColors.greyDark.withOpacity(0.2),
      margin: margin ?? const EdgeInsets.symmetric(horizontal: 10),
      padding: padding ?? const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius ?? 15),
      ),
      behavior: SnackBarBehavior.floating,
      dismissDirection: DismissDirection.horizontal,
    );

    if (instantInit) {
      return ScaffoldMessenger.of(context).showSnackBar(snackBar);
    } else {
      late ScaffoldFeatureController<SnackBar, SnackBarClosedReason> controller;
      SchedulerBinding.instance.addPostFrameCallback((_) {
        controller = ScaffoldMessenger.of(context).showSnackBar(snackBar);
      });
      return controller;
    }
  }
}
