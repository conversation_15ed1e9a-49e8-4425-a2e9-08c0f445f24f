/// Custom Icon Button Widget
///
/// Provides a standardized button with an icon and text
/// Used throughout the application for actions that need visual indicators
library custom_icon_button;

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_text_styles.dart';

/// Custom Icon Button Widget
///
/// A reusable button component with an icon and text
/// Provides consistent styling with the app's design language
class CustomIconButton extends StatefulWidget {
  /// Text to display on the button
  final String text;

  /// Function to execute when the button is pressed
  final Function onPressed;

  /// Height of the button
  final double height;

  /// Background color of the button (defaults to primary color)
  final Color? bgColor;

  /// Text color of the button (defaults to white)
  final Color? textColor;

  /// SVG icon path to display on the button
  final String? icon;

  /// Whether to show a border around the button
  final bool? isBorder;
  /// Creates a CustomIconButton
  ///
  /// @param text Text to display on the button
  /// @param onPressed Function to execute when the button is pressed
  /// @param height Height of the button
  /// @param bgColor Optional background color (defaults to primary color)
  /// @param textColor Optional text color (defaults to white)
  /// @param icon Optional SVG icon path to display
  /// @param isBorder Optional flag to show a border (defaults to false)
  const CustomIconButton(
      {super.key,
      required this.text,
      required this.onPressed,
      required this.height,
      this.bgColor,
      this.textColor,
      this.icon,
      this.isBorder});

  @override
  CustomIconButtonState createState() => CustomIconButtonState();
}

/// State class for CustomIconButton
class CustomIconButtonState extends State<CustomIconButton> {
  @override
  Widget build(BuildContext context) {
    // BUTTON LAYOUT ----------
    return SizedBox(
      // Set dimensions
      height: widget.height,
      width: double.infinity,

      // Button implementation
      child: ElevatedButton(
        // Handle button press
        onPressed: () => widget.onPressed(),

        // Button styling
        style: ElevatedButton.styleFrom(
            // Shadow color
            shadowColor: AppColors.greyLight,

            // Shape with conditional border
            shape: widget.isBorder ?? false
                ? RoundedRectangleBorder(
                    // With border
                    side: const BorderSide(
                        color: AppColors.greyLight, width: 1),
                    borderRadius: BorderRadius.circular(7.5),
                  )
                : RoundedRectangleBorder(
                    // Without border
                    borderRadius: BorderRadius.circular(7.5),
                  ),

            // Background color with fallback to primary color
            backgroundColor: widget.bgColor ?? AppColors.primaryPurple),

        // Button content
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            SvgPicture.asset(widget.icon!),

            // Spacing
            const SizedBox(
              width: 11.25,
            ),

            // Text
            Text(widget.text,
                style: styleWhiteLarge.copyWith(
                  // Text color with fallback to white
                  color: widget.textColor ?? AppColors.whitePure,
                )),
          ],
        ),
      ),
    );
    // BUTTON LAYOUT ##########
  }
}
