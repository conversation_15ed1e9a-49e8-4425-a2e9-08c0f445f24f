/// Location Service
///
/// Provides methods for handling location-related operations
/// Centralizes location access, permissions, and geocoding
///
/// This service handles location permissions, fetching, and geocoding
library location_service;

import 'package:flutter/foundation.dart';
import 'package:geocoding/geocoding.dart' as geo;
import 'package:location/location.dart' as location_lib;
import 'package:permission_handler/permission_handler.dart';

/// Location data model
///
/// Contains location coordinates and address information
class LocationData {
  final double lat;
  final double lng;
  final String country;
  final String city;
  final String district;

  LocationData({
    required this.lat,
    required this.lng,
    required this.country,
    required this.city,
    required this.district,
  });

  /// Convert to a map for storage or API calls
  Map<String, dynamic> toMap() {
    return {
      'lat': lat,
      'lng': lng,
      'country': country,
      'city': city,
      'district': district,
    };
  }
}

/// Location Service Interface
///
/// Defines the contract for location operations
/// Allows for easy mocking in tests
abstract class LocationService {
  /// Check if location services are enabled
  ///
  /// @return A Future that resolves to true if enabled, false otherwise
  Future<bool> isLocationServiceEnabled();

  /// Check if location permission is granted
  ///
  /// @return A Future that resolves to true if granted, false otherwise
  Future<bool> isLocationPermissionGranted();

  /// Request location permission
  ///
  /// @return A Future that resolves to true if granted, false otherwise
  Future<bool> requestLocationPermission();

  /// Get the current location
  ///
  /// @return A Future that resolves to a LocationData object
  /// @throws Exception if location services are disabled or permission is denied
  Future<LocationData> getCurrentLocation();

  /// Get a mock location for testing or simulator use
  ///
  /// @return A LocationData object with mock values
  LocationData getMockLocation();
}

/// Location Service Implementation
///
/// Implements the LocationService interface using location and geocoding packages
class LocationServiceImpl implements LocationService {
  final location_lib.Location _location;

  /// Constructor that takes a Location instance
  ///
  /// @param location The Location instance to use
  LocationServiceImpl({
    required location_lib.Location location,
  }) : _location = location;

  @override
  Future<bool> isLocationServiceEnabled() async {
    try {
      return await _location.serviceEnabled();
    } catch (e) {
      if (kDebugMode) {
        print('Error checking location service: $e');
      }
      return false;
    }
  }

  @override
  Future<bool> isLocationPermissionGranted() async {
    try {
      location_lib.PermissionStatus permissionStatus = await _location.hasPermission();
      return permissionStatus == location_lib.PermissionStatus.granted;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking location permission: $e');
      }
      return false;
    }
  }

  @override
  Future<bool> requestLocationPermission() async {
    try {
      // Check if service is enabled
      bool serviceEnabled = await _location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await _location.requestService();
        if (!serviceEnabled) {
          return false;
        }
      }

      // Check permission
      location_lib.PermissionStatus permissionStatus = await _location.hasPermission();
      if (permissionStatus == location_lib.PermissionStatus.denied) {
        permissionStatus = await _location.requestPermission();
        if (permissionStatus != location_lib.PermissionStatus.granted) {
          // Try with permission_handler as a fallback
          if (await Permission.location.request().isGranted) {
            return true;
          }
          return false;
        }
      }

      return permissionStatus == location_lib.PermissionStatus.granted;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting location permission: $e');
      }
      return false;
    }
  }

  @override
  Future<LocationData> getCurrentLocation() async {
    try {
      // Check if service is enabled
      bool serviceEnabled = await isLocationServiceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await _location.requestService();
        if (!serviceEnabled) {
          throw Exception('Location services are disabled');
        }
      }

      // Check permission
      bool permissionGranted = await isLocationPermissionGranted();
      if (!permissionGranted) {
        permissionGranted = await requestLocationPermission();
        if (!permissionGranted) {
          throw Exception('Location permission denied');
        }
      }

      // Get location
      location_lib.LocationData position = await _location.getLocation();
      
      // Set locale for Arabic place names
      geo.setLocaleIdentifier("ar_SA");

      // Convert coordinates to human-readable location
      List<geo.Placemark> placemarks = await geo.placemarkFromCoordinates(
        position.latitude!,
        position.longitude!,
      );

      // Extract address components
      String country = placemarks.first.country ?? '';
      String city = placemarks.first.locality ?? '';
      String district = placemarks.first.subLocality ?? '';

      return LocationData(
        lat: position.latitude!,
        lng: position.longitude!,
        country: country,
        city: city,
        district: district,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting location: $e');
      }
      
      // Return mock location in debug mode
      if (kDebugMode) {
        return getMockLocation();
      }
      
      rethrow;
    }
  }

  @override
  LocationData getMockLocation() {
    return LocationData(
      lat: 24.680459665769533,
      lng: 46.579983324072145,
      country: "المملكة العربية السعودية",
      city: "الرياض",
      district: "عرقة",
    );
  }
}
