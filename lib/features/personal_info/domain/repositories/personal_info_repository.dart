/// Personal Info Repository Interface
///
/// Defines the contract for personal information data operations
/// Implementations should handle data source specifics (Firestore, API, etc.)
library personal_info_repository;

import '../entities/personal_info_entity.dart';

/// Abstract repository for personal info data operations
///
/// Defines the interface for personal information-related data operations
/// Implementations should handle user profile data, validation, and persistence
abstract class PersonalInfoRepository {
  /// Get user's personal information
  ///
  /// Retrieves the user's personal information from the data source
  ///
  /// @param userId User identifier
  /// @return A Future that resolves to PersonalInfoEntity or null if not found
  /// @throws PersonalInfoException if retrieval fails
  Future<PersonalInfoEntity?> getPersonalInfo(String userId);

  /// Update user's personal information
  ///
  /// Saves the user's personal information to the data source
  ///
  /// @param userId User identifier
  /// @param personalInfo PersonalInfoEntity to save
  /// @return A Future that completes when personal info is saved
  /// @throws PersonalInfoException if saving fails
  Future<void> updatePersonalInfo(
      String userId, PersonalInfoEntity personalInfo);

  /// Update user's gender
  ///
  /// Updates only the user's gender field
  ///
  /// @param userId User identifier
  /// @param gender New gender
  /// @return A Future that completes when gender is updated
  /// @throws PersonalInfoException if update fails
  Future<void> updateGender(String userId, String gender);

  /// Update user's birth year
  ///
  /// Updates only the user's birth year field
  ///
  /// @param userId User identifier
  /// @param birthYear New birth year
  /// @return A Future that completes when birth year is updated
  /// @throws PersonalInfoException if update fails
  Future<void> updateBirthYear(String userId, String birthYear);

  /// Update user's nationality
  ///
  /// Updates only the user's nationality field
  ///
  /// @param userId User identifier
  /// @param nationality New nationality
  /// @return A Future that completes when nationality is updated
  /// @throws PersonalInfoException if update fails
  Future<void> updateNationality(String userId, String nationality);

  /// Check if personal info is complete
  ///
  /// Verifies that all required personal information fields are filled
  ///
  /// @param userId User identifier
  /// @return A Future that resolves to true if personal info is complete
  /// @throws PersonalInfoException if check fails
  Future<bool> isPersonalInfoComplete(String userId);

  /// Get personal info completion percentage
  ///
  /// Calculates how much of the personal info is completed
  ///
  /// @param userId User identifier
  /// @return A Future that resolves to completion percentage (0.0 to 1.0)
  /// @throws PersonalInfoException if calculation fails
  Future<double> getCompletionPercentage(String userId);

  /// Get missing personal info fields
  ///
  /// Returns a list of fields that are missing or incomplete
  ///
  /// @param userId User identifier
  /// @return A Future that resolves to a list of missing field names
  /// @throws PersonalInfoException if check fails
  Future<List<String>> getMissingFields(String userId);

  /// Validate personal information
  ///
  /// Validates that the personal information is complete and accurate
  ///
  /// @param personalInfo PersonalInfoEntity to validate
  /// @return PersonalInfoValidationResult indicating validation status
  PersonalInfoValidationResult validatePersonalInfo(
      PersonalInfoEntity personalInfo);

  /// Get supported nationalities
  ///
  /// Retrieves a list of supported nationality options
  ///
  /// @return A Future that resolves to a list of nationality names
  Future<List<String>> getSupportedNationalities();

  /// Get supported genders
  ///
  /// Retrieves a list of supported gender options
  ///
  /// @return A Future that resolves to a list of gender values
  Future<List<String>> getSupportedGenders();

  /// Get valid birth year range
  ///
  /// Returns the valid range for birth years
  ///
  /// @return A Future that resolves to a map with 'min' and 'max' years
  Future<Map<String, int>> getValidBirthYearRange();

  /// Search nationalities
  ///
  /// Searches for nationalities matching the query
  ///
  /// @param query Search query
  /// @return A Future that resolves to a list of matching nationality names
  Future<List<String>> searchNationalities(String query);

  /// Clear personal information
  ///
  /// Removes all personal information for the user (used during account deletion)
  ///
  /// @param userId User identifier
  /// @return A Future that completes when personal info is cleared
  /// @throws PersonalInfoException if clearing fails
  Future<void> clearPersonalInfo(String userId);

  /// Export personal information
  ///
  /// Exports user's personal information for backup or transfer
  ///
  /// @param userId User identifier
  /// @return A Future that resolves to a Map containing personal info data
  /// @throws PersonalInfoException if export fails
  Future<Map<String, dynamic>> exportPersonalInfo(String userId);

  /// Import personal information
  ///
  /// Imports user's personal information from backup data
  ///
  /// @param userId User identifier
  /// @param personalInfoData Map containing personal info data to import
  /// @return A Future that completes when personal info is imported
  /// @throws PersonalInfoException if import fails
  Future<void> importPersonalInfo(
      String userId, Map<String, dynamic> personalInfoData);

  /// Listen to personal info changes
  ///
  /// Creates a stream that emits when personal info is updated
  ///
  /// @param userId User identifier
  /// @return Stream of PersonalInfoEntity that emits on changes
  Stream<PersonalInfoEntity?> watchPersonalInfo(String userId);
}

/// Personal Info Exception
///
/// Custom exception for personal info-related errors
class PersonalInfoException implements Exception {
  /// Error message describing what went wrong
  final String message;

  /// Error code for programmatic handling
  final String? code;

  /// Creates a PersonalInfoException
  ///
  /// @param message Error message
  /// @param code Optional error code
  const PersonalInfoException(this.message, {this.code});

  @override
  String toString() =>
      'PersonalInfoException: $message${code != null ? ' (Code: $code)' : ''}';
}
