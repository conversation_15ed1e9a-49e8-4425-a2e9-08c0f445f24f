/// User Provider
///
/// Provides Riverpod state management for user profile functionality
/// Replaces UserViewModel with Riverpod architecture
library user_provider;

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/features/profile/presentation/providers/user_repository_provider.dart';
import 'package:towasl/features/core/presentation/providers/use_case_provider.dart';
import 'package:towasl/features/core/presentation/providers/app_state_provider.dart';
import 'package:towasl/shared/models/user_model.dart';
import 'package:towasl/shared/widgets/toasts_custom.dart';

part 'user_provider.g.dart';

/// State class for user profile management
class UserState {
  /// Current user model
  final UserModel? userModel;

  /// Whether user data is loading
  final bool isLoading;

  /// Whether user data is being saved
  final bool isSaving;

  /// Whether user data is being updated
  final bool isUpdating;

  /// Error message if any
  final String? errorMessage;

  const UserState({
    this.userModel,
    this.isLoading = false,
    this.isSaving = false,
    this.isUpdating = false,
    this.errorMessage,
  });

  UserState copyWith({
    UserModel? userModel,
    bool? isLoading,
    bool? isSaving,
    bool? isUpdating,
    String? errorMessage,
  }) {
    return UserState(
      userModel: userModel ?? this.userModel,
      isLoading: isLoading ?? this.isLoading,
      isSaving: isSaving ?? this.isSaving,
      isUpdating: isUpdating ?? this.isUpdating,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Clear error message
  UserState clearError() {
    return copyWith(errorMessage: null);
  }
}

/// User Notifier
///
/// Manages user profile state and business logic
/// Follows MVVM pattern with Riverpod state management
@riverpod
class User extends _$User {
  @override
  UserState build() {
    if (kDebugMode) {
      print('UserNotifier: Initialized');
    }
    return const UserState();
  }

  /// Load user data by user ID
  Future<void> loadUserData(String userId) async {
    if (kDebugMode) {
      print('UserNotifier: Loading user data for: $userId');
    }

    try {
      state = state.copyWith(isLoading: true);

      // Get user repository
      final userRepository = ref.read(userRepositoryProvider);

      // Load user data
      final userModel = await userRepository.getUserById(userId);

      if (userModel != null) {
        state = state.copyWith(
          userModel: userModel,
          isLoading: false,
        );

        // Update app state with user model
        await ref.read(appStateProvider.notifier).setUserModel(userModel);

        if (kDebugMode) {
          print('UserNotifier: User data loaded successfully');
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'User not found',
        );

        if (kDebugMode) {
          print('UserNotifier: User not found');
        }
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load user data',
      );

      if (kDebugMode) {
        print('UserNotifier: Error loading user data - $e');
      }
    }
  }

  /// Save user data
  Future<bool> saveUserData(UserModel userModel) async {
    if (kDebugMode) {
      print('UserNotifier: Saving user data: ${userModel.userId}');
    }

    try {
      state = state.copyWith(isSaving: true);

      // Get user repository
      final userRepository = ref.read(userRepositoryProvider);

      // Save user data
      final success = await userRepository.saveUser(userModel);

      if (success) {
        state = state.copyWith(
          userModel: userModel,
          isSaving: false,
        );

        // Update app state with user model
        await ref.read(appStateProvider.notifier).setUserModel(userModel);

        if (kDebugMode) {
          print('UserNotifier: User data saved successfully');
        }

        ToastCustom.successToast('تم حفظ الملف الشخصي بنجاح!');
        return true;
      } else {
        state = state.copyWith(
          isSaving: false,
          errorMessage: 'Failed to save user data',
        );

        if (kDebugMode) {
          print('UserNotifier: Failed to save user data');
        }

        ToastCustom.errorToast('فشل في حفظ الملف الشخصي');
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isSaving: false,
        errorMessage: 'An error occurred while saving',
      );

      if (kDebugMode) {
        print('UserNotifier: Error saving user data - $e');
      }

      ToastCustom.errorToast('An error occurred while saving');
      return false;
    }
  }

  /// Update user interests
  Future<bool> updateUserInterests(
      String userId, Map<String, List<String>> interests) async {
    if (kDebugMode) {
      print('UserNotifier: Updating user interests for: $userId');
    }

    try {
      state = state.copyWith(isUpdating: true);

      // Get user profile use case
      final userProfileUseCase = ref.read(userProfileUseCaseProvider);

      // Update interests
      final success =
          await userProfileUseCase.updateUserInterests(userId, interests);

      if (success) {
        // Reload user data to get updated profile
        await loadUserData(userId);

        if (kDebugMode) {
          print('UserNotifier: User interests updated successfully');
        }

        ToastCustom.successToast('Interests updated successfully!');
        return true;
      } else {
        state = state.copyWith(
          isUpdating: false,
          errorMessage: 'Failed to update interests',
        );

        if (kDebugMode) {
          print('UserNotifier: Failed to update interests');
        }

        ToastCustom.errorToast('Failed to update interests');
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isUpdating: false,
        errorMessage: 'An error occurred while updating interests',
      );

      if (kDebugMode) {
        print('UserNotifier: Error updating interests - $e');
      }

      ToastCustom.errorToast('An error occurred while updating interests');
      return false;
    }
  }

  /// Update user location
  Future<bool> updateUserLocation(String userId, UserLocation location) async {
    if (kDebugMode) {
      print('UserNotifier: Updating user location for: $userId');
    }

    try {
      state = state.copyWith(isUpdating: true);

      // Get user profile use case
      final userProfileUseCase = ref.read(userProfileUseCaseProvider);

      // Update location
      final success =
          await userProfileUseCase.updateUserLocation(userId, location);

      if (success) {
        // Reload user data to get updated profile
        await loadUserData(userId);

        if (kDebugMode) {
          print('UserNotifier: User location updated successfully');
        }

        ToastCustom.successToast('Location updated successfully!');
        return true;
      } else {
        state = state.copyWith(
          isUpdating: false,
          errorMessage: 'Failed to update location',
        );

        if (kDebugMode) {
          print('UserNotifier: Failed to update location');
        }

        ToastCustom.errorToast('Failed to update location');
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isUpdating: false,
        errorMessage: 'An error occurred while updating location',
      );

      if (kDebugMode) {
        print('UserNotifier: Error updating location - $e');
      }

      ToastCustom.errorToast('An error occurred while updating location');
      return false;
    }
  }

  /// Update personal information
  Future<bool> updatePersonalInfo(
    String userId, {
    String? birthdayYear,
    String? gender,
    String? nationality,
  }) async {
    if (kDebugMode) {
      print('UserNotifier: Updating personal info for: $userId');
    }

    try {
      state = state.copyWith(isUpdating: true);

      // Get user profile use case
      final userProfileUseCase = ref.read(userProfileUseCaseProvider);

      // Update personal info
      final success = await userProfileUseCase.updatePersonalInfo(
        userId,
        birthdayYear: birthdayYear,
        gender: gender,
        nationality: nationality,
      );

      if (success) {
        // Reload user data to get updated profile
        await loadUserData(userId);

        if (kDebugMode) {
          print('UserNotifier: Personal info updated successfully');
        }

        ToastCustom.successToast('Personal info updated successfully!');
        return true;
      } else {
        state = state.copyWith(
          isUpdating: false,
          errorMessage: 'Failed to update personal info',
        );

        if (kDebugMode) {
          print('UserNotifier: Failed to update personal info');
        }

        ToastCustom.errorToast('Failed to update personal info');
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isUpdating: false,
        errorMessage: 'An error occurred while updating personal info',
      );

      if (kDebugMode) {
        print('UserNotifier: Error updating personal info - $e');
      }

      ToastCustom.errorToast('An error occurred while updating personal info');
      return false;
    }
  }

  /// Clear error message
  void clearError() {
    state = state.clearError();
  }

  /// Refresh user data
  Future<void> refreshUserData() async {
    final userId = ref.read(userIdProvider);
    if (userId.isNotEmpty) {
      await loadUserData(userId);
    }
  }
}

// ============================================================================
// CONVENIENCE PROVIDERS
// ============================================================================

/// Provider for current user model
@riverpod
UserModel? currentUserModel(CurrentUserModelRef ref) {
  return ref.watch(userProvider).userModel;
}

/// Provider for user loading state
@riverpod
bool isUserLoading(IsUserLoadingRef ref) {
  return ref.watch(userProvider).isLoading;
}

/// Provider for user saving state
@riverpod
bool isUserSaving(IsUserSavingRef ref) {
  return ref.watch(userProvider).isSaving;
}

/// Provider for user updating state
@riverpod
bool isUserUpdating(IsUserUpdatingRef ref) {
  return ref.watch(userProvider).isUpdating;
}

/// Provider for user error message
@riverpod
String? userErrorMessage(UserErrorMessageRef ref) {
  return ref.watch(userProvider).errorMessage;
}
