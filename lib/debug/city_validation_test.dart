/// City Validation Test Widget
///
/// A simple test widget to debug city validation functionality
/// Use this to test if the city validation service is working correctly
library city_validation_test;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/shared/providers/city_validation_provider.dart';

/// City Validation Test View
///
/// Simple test interface to validate cities manually
class CityValidationTestView extends ConsumerStatefulWidget {
  const CityValidationTestView({super.key});

  @override
  ConsumerState<CityValidationTestView> createState() => _CityValidationTestViewState();
}

class _CityValidationTestViewState extends ConsumerState<CityValidationTestView> {
  final _cityController = TextEditingController();
  
  @override
  void dispose() {
    _cityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cityValidationState = ref.watch(cityValidationProvider);
    final isValidating = cityValidationState.isValidating;
    final lastResult = cityValidationState.lastResult;

    return Scaffold(
      appBar: AppBar(
        title: const Text('City Validation Test'),
        backgroundColor: AppColors.primaryPurple,
        foregroundColor: AppColors.whitePure,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Test City Validation',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.greyDark,
              ),
            ),
            const SizedBox(height: 20),
            
            // City input field
            TextField(
              controller: _cityController,
              decoration: const InputDecoration(
                labelText: 'Enter City Name',
                hintText: 'e.g., Riyadh, TestUnsupportedCity',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            
            // Test button
            ElevatedButton(
              onPressed: isValidating ? null : _testCity,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryPurple,
                foregroundColor: AppColors.whitePure,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: isValidating 
                ? const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.whitePure),
                        ),
                      ),
                      SizedBox(width: 8),
                      Text('Validating...'),
                    ],
                  )
                : const Text('Test City Validation'),
            ),
            const SizedBox(height: 20),
            
            // Results section
            if (lastResult != null) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: lastResult.isSupported 
                    ? Colors.green.shade50 
                    : Colors.red.shade50,
                  border: Border.all(
                    color: lastResult.isSupported 
                      ? Colors.green.shade200 
                      : Colors.red.shade200,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          lastResult.isSupported 
                            ? Icons.check_circle 
                            : Icons.error,
                          color: lastResult.isSupported 
                            ? Colors.green.shade600 
                            : Colors.red.shade600,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          lastResult.isSupported ? 'SUPPORTED' : 'NOT SUPPORTED',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: lastResult.isSupported 
                              ? Colors.green.shade700 
                              : Colors.red.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      lastResult.message,
                      style: TextStyle(
                        color: lastResult.isSupported 
                          ? Colors.green.shade700 
                          : Colors.red.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            const SizedBox(height: 20),
            
            // Load supported cities button
            ElevatedButton(
              onPressed: _loadSupportedCities,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.greyMedium,
                foregroundColor: AppColors.whitePure,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('Load Supported Cities'),
            ),
            
            // Show supported cities
            if (cityValidationState.citiesLoaded) ...[
              const SizedBox(height: 16),
              const Text(
                'Supported Cities:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.greyDark,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  border: Border.all(color: Colors.blue.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  cityValidationState.supportedCities.isEmpty 
                    ? 'No supported cities found. Check Firestore setup.'
                    : cityValidationState.supportedCities.join(', '),
                  style: TextStyle(
                    color: Colors.blue.shade700,
                  ),
                ),
              ),
            ],
            
            // Error message
            if (cityValidationState.errorMessage != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  border: Border.all(color: Colors.orange.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Error: ${cityValidationState.errorMessage}',
                  style: TextStyle(
                    color: Colors.orange.shade700,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  void _testCity() {
    final city = _cityController.text.trim();
    if (city.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a city name')),
      );
      return;
    }
    
    if (kDebugMode) {
      print('CityValidationTest: Testing city: $city');
    }
    
    ref.read(cityValidationProvider.notifier).validateCity(city);
  }
  
  void _loadSupportedCities() {
    if (kDebugMode) {
      print('CityValidationTest: Loading supported cities');
    }
    
    ref.read(cityValidationProvider.notifier).loadSupportedCities();
  }
}
