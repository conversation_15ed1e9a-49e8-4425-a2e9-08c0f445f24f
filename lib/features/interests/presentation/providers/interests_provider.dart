/// Interests Provider
///
/// Provides Riverpod state management for interests selection functionality
/// Replaces InterestsViewModel with Riverpod architecture
library interests_provider;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/core/providers/navigation_provider.dart';
import 'package:towasl/core/providers/service_providers.dart';
import 'package:towasl/core/routes.dart';
import 'package:towasl/features/core/presentation/providers/app_state_provider.dart';
import 'package:towasl/features/interests/data/models/interests_display_model.dart';
import 'package:towasl/features/interests/presentation/providers/interests_repository_provider.dart';
import 'package:towasl/shared/models/selected_interest_model.dart';
import 'package:towasl/shared/widgets/toasts_custom.dart';

part 'interests_provider.g.dart';

/// State class for interests selection
class InterestsState {
  /// Available interests display model
  final InterestsDisplayModel? displayModel;

  /// Selected interests with category and subcategory IDs
  final List<SelectedInterest> selectedInterests;

  /// Whether interests are loading
  final bool isLoading;

  /// Whether interests are being saved
  final bool isSaving;

  /// Error message if any
  final String? errorMessage;

  const InterestsState({
    this.displayModel,
    this.selectedInterests = const [],
    this.isLoading = false,
    this.isSaving = false,
    this.errorMessage,
  });

  InterestsState copyWith({
    InterestsDisplayModel? displayModel,
    List<SelectedInterest>? selectedInterests,
    bool? isLoading,
    bool? isSaving,
    String? errorMessage,
  }) {
    return InterestsState(
      displayModel: displayModel ?? this.displayModel,
      selectedInterests: selectedInterests ?? this.selectedInterests,
      isLoading: isLoading ?? this.isLoading,
      isSaving: isSaving ?? this.isSaving,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Clear error message
  InterestsState clearError() {
    return copyWith(errorMessage: null);
  }

  /// Get total number of selected interests
  int get totalSelectedInterests {
    return selectedInterests.length;
  }

  /// Check if minimum interests are selected (at least 3)
  bool get hasMinimumInterests {
    return totalSelectedInterests >= 3;
  }

  /// Check if a specific subcategory is selected
  bool isSubCategorySelected(String categoryId, String subCategoryId) {
    return selectedInterests.any((interest) =>
        interest.categoryId == categoryId &&
        interest.subcategoryId == subCategoryId);
  }
}

/// Interests Notifier
///
/// Manages interests selection state and business logic
/// Follows MVVM pattern with Riverpod state management
@Riverpod(keepAlive: true)
class Interests extends _$Interests {
  @override
  InterestsState build() {
    if (kDebugMode) {
      print('InterestsNotifier: Initialized');
    }

    // Return initial state - interests will be loaded via explicit calls
    return const InterestsState();
  }

  /// Load display interests from repository
  Future<void> loadDisplayInterests() async {
    try {
      state = state.copyWith(isLoading: true);

      if (kDebugMode) {
        print('InterestsNotifier: Loading display interests');
      }

      // For now, use KSA_ar as default country/language
      // Later get this from user settings or app configuration
      const countryLang = 'KSA_ar';

      final repository = ref.read(interestsRepositoryProvider);
      final displayModel = await repository.getDisplayInterests(countryLang);

      state = state.copyWith(
        displayModel: displayModel,
        isLoading: false,
      );

      if (kDebugMode) {
        print(
            'InterestsNotifier: Display interests loaded with ${displayModel.categories.length} categories');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load interests',
      );

      if (kDebugMode) {
        print('InterestsNotifier: Error loading display interests - $e');
      }
    }
  }

  /// Load user's existing interests
  Future<void> loadUserInterests(String userId) async {
    if (kDebugMode) {
      print('InterestsNotifier: Loading user interests for: $userId');
    }

    try {
      // Don't set loading to true if we already have display interests
      // This prevents the UI from showing loading state when just refreshing user interests
      if (state.displayModel == null) {
        state = state.copyWith(isLoading: true);
      }

      // Load user interests directly from repository
      final repository = ref.read(interestsRepositoryProvider);
      final userInterests = await repository.getUserInterests(userId);

      state = state.copyWith(
        selectedInterests: userInterests,
        isLoading: false,
      );

      if (kDebugMode) {
        print(
            'InterestsNotifier: User interests loaded: ${userInterests.length} interests');
        for (final interest in userInterests) {
          print('  - ${interest.categoryId}/${interest.subcategoryId}');
        }
      }
    } catch (e) {
      // Handle errors gracefully
      state = state.copyWith(
        selectedInterests: [],
        isLoading: false,
        errorMessage: null, // Don't show error for initialization issues
      );

      if (kDebugMode) {
        print('InterestsNotifier: Error loading user interests - $e');
        print('InterestsNotifier: Starting with empty interests state');
      }
    }
  }

  /// Toggle subcategory selection
  void toggleSubCategory(String categoryId, String subCategoryId) {
    final currentSelected =
        List<SelectedInterest>.from(state.selectedInterests);

    // Check if this subcategory is already selected
    final existingIndex = currentSelected.indexWhere((interest) =>
        interest.categoryId == categoryId &&
        interest.subcategoryId == subCategoryId);

    if (existingIndex >= 0) {
      // Remove interest
      currentSelected.removeAt(existingIndex);
    } else {
      // Add interest
      currentSelected.add(SelectedInterest(
        categoryId: categoryId,
        subcategoryId: subCategoryId,
        selectedAt: Timestamp.now(),
      ));
    }

    state = state.copyWith(selectedInterests: currentSelected);

    if (kDebugMode) {
      print('InterestsNotifier: Toggled $subCategoryId in $categoryId');
      print(
          'InterestsNotifier: Total selected: ${state.totalSelectedInterests}');
    }
  }

  /// Check if a subcategory is selected
  bool isSubCategorySelected(String categoryId, String subCategoryId) {
    return state.isSubCategorySelected(categoryId, subCategoryId);
  }

  /// Save selected interests
  Future<void> saveInterests({bool isFromSetting = false}) async {
    if (!state.hasMinimumInterests) {
      state =
          state.copyWith(errorMessage: 'Please select at least 3 interests');
      ToastCustom.errorToast('Please select at least 3 interests');
      return;
    }

    // Try to get user ID from multiple sources
    String userId = '';

    // First try from app state provider
    try {
      userId = ref.read(userIdProvider);
    } catch (e) {
      if (kDebugMode) {
        print(
            'InterestsNotifier: Could not get user ID from app state provider - $e');
      }
    }

    // If still empty, try from storage service directly
    if (userId.isEmpty) {
      try {
        final storageService = ref.read(storageServiceProvider);
        userId = storageService.getUserIDValue();
        if (kDebugMode) {
          print('InterestsNotifier: Got user ID from storage: $userId');
        }
      } catch (e) {
        if (kDebugMode) {
          print('InterestsNotifier: Could not get user ID from storage - $e');
        }
      }
    }

    if (userId.isEmpty) {
      state = state.copyWith(errorMessage: 'User not found');
      ToastCustom.errorToast('User not found');
      if (kDebugMode) {
        print('InterestsNotifier: No user ID found from any source');
      }
      return;
    }

    if (kDebugMode) {
      print('InterestsNotifier: Saving interests for user: $userId');
      print(
          'InterestsNotifier: Selected interests: ${state.selectedInterests}');
    }

    try {
      state = state.copyWith(isSaving: true);

      // Save interests through repository
      final repository = ref.read(interestsRepositoryProvider);
      await repository.saveUserInterests(userId, state.selectedInterests);

      state = state.copyWith(isSaving: false);

      if (kDebugMode) {
        print('InterestsNotifier: Interests saved successfully');
      }

      // Show appropriate success message and navigate
      if (isFromSetting) {
        // Show update success message and go back
        _showInterestsUpdatedSuccessMessage();
        _navigateBack();
      } else {
        // Navigate to next step (personal info) during onboarding
        _navigateToPersonalInfo();
      }
    } catch (e) {
      state = state.copyWith(
        isSaving: false,
        errorMessage: 'An error occurred while saving interests',
      );

      if (kDebugMode) {
        print('InterestsNotifier: Error saving interests - $e');
      }

      ToastCustom.errorToast('An error occurred while saving interests');
    }
  }

  /// Navigate to personal info screen
  void _navigateToPersonalInfo() {
    if (kDebugMode) {
      print('InterestsNotifier: Navigating to personal info screen');
    }

    ref
        .read(navigationProvider.notifier)
        .navigateToReplacement(AppRoutes.personalInfo);
  }

  /// Navigate back (when coming from settings)
  void _navigateBack() {
    if (kDebugMode) {
      print('InterestsNotifier: Navigating back to previous screen');
    }

    ref.read(navigationProvider.notifier).goBack();
  }

  /// Show interests updated success message
  void _showInterestsUpdatedSuccessMessage() {
    // Use hardcoded Arabic message for now since we don't have context access
    // Later consider using a different approach for localized messages in providers
    ToastCustom.successToast('تم تحديث الاهتمامات بنجاح');
  }

  /// Clear error message
  void clearError() {
    state = state.clearError();
  }

  /// Reset selections
  void resetSelections() {
    state = state.copyWith(selectedInterests: []);

    if (kDebugMode) {
      print('InterestsNotifier: Selections reset');
    }
  }
}

// ============================================================================
// CONVENIENCE PROVIDERS
// ============================================================================

/// Provider for display interests model
@Riverpod(keepAlive: true)
InterestsDisplayModel? displayInterests(DisplayInterestsRef ref) {
  return ref.watch(interestsProvider).displayModel;
}

/// Provider for selected interests
@Riverpod(keepAlive: true)
List<SelectedInterest> selectedInterests(SelectedInterestsRef ref) {
  return ref.watch(interestsProvider).selectedInterests;
}

/// Provider for interests loading state
@Riverpod(keepAlive: true)
bool isInterestsLoading(IsInterestsLoadingRef ref) {
  return ref.watch(interestsProvider).isLoading;
}

/// Provider for interests saving state
@Riverpod(keepAlive: true)
bool isInterestsSaving(IsInterestsSavingRef ref) {
  return ref.watch(interestsProvider).isSaving;
}

/// Provider for total selected interests count
@Riverpod(keepAlive: true)
int totalSelectedInterests(TotalSelectedInterestsRef ref) {
  return ref.watch(interestsProvider).totalSelectedInterests;
}

/// Provider for minimum interests validation
@Riverpod(keepAlive: true)
bool hasMinimumInterests(HasMinimumInterestsRef ref) {
  return ref.watch(interestsProvider).hasMinimumInterests;
}

/// Provider for interests error message
@Riverpod(keepAlive: true)
String? interestsErrorMessage(InterestsErrorMessageRef ref) {
  return ref.watch(interestsProvider).errorMessage;
}
