/// Country Selection Helpers
///
/// Provides utility functions for country selection and search functionality
/// Used in various parts of the app for nationality and phone number selection
library country_helpers;

import 'package:towasl/shared/helpers/internationalization/country_data.dart';
import 'package:towasl/shared/helpers/text_helpers.dart';

/// Extension methods for List<Country>
///
/// Adds search and filtering functionality to lists of Country objects
extension CountryListExtensions on List<Country> {
  /// Searches for countries matching a search string
  ///
  /// Handles both numeric searches (for dial codes) and text searches (for names)
  /// Normalizes text by removing diacritics and converting to lowercase
  ///
  /// @param search The search string
  /// @return A filtered list of countries matching the search criteria
  List<Country> searchCountries(String search) {
    if (search.isEmpty) return this;
    
    // Normalize the search string
    final normalizedSearch = TextHelpers.normalizeForSearch(search);

    return where((country) {
      // If search is numeric or starts with +, search in dial codes
      if (TextHelpers.isNumeric(search) || search.startsWith("+")) {
        return country.dialCode.contains(search.replaceAll("+", ""));
      }
      
      // Otherwise search in country names (both English and translations)
      final normalizedName = TextHelpers.normalizeForSearch(
        country.name.replaceAll("+", "")
      );
      
      if (normalizedName.contains(normalizedSearch)) {
        return true;
      }
      
      // Search in translations
      return country.nameTranslations.values.any((translation) =>
          TextHelpers.normalizeForSearch(translation).contains(normalizedSearch));
    }).toList();
  }

  /// Filters countries by a specific region
  ///
  /// @param region The region to filter by (e.g., 'Middle East', 'Europe')
  /// @return A filtered list of countries in the specified region
  List<Country> filterByRegion(String region) {
    // This would need to be implemented based on how regions are defined
    // For now, return all countries
    return this;
  }

  /// Sorts countries alphabetically by their localized names
  ///
  /// @param languageCode The language code for localization
  /// @return A sorted list of countries
  List<Country> sortByLocalizedName(String languageCode) {
    final sortedList = List<Country>.from(this);
    sortedList.sort((a, b) => 
      a.getLocalizedName(languageCode).compareTo(b.getLocalizedName(languageCode)));
    return sortedList;
  }
}

/// General country selection utilities
class CountryHelpers {
  /// Get Saudi Arabia country object
  ///
  /// @return The Saudi Arabia country object
  static Country getSaudiArabia() {
    return countries.firstWhere(
      (country) => country.code == 'SA',
      orElse: () => countries.first,
    );
  }

  /// Find a country by its country code
  ///
  /// @param code The ISO country code (e.g., 'SA', 'US', 'GB')
  /// @return The country object or null if not found
  static Country? findByCode(String code) {
    try {
      return countries.firstWhere(
        (country) => country.code.toUpperCase() == code.toUpperCase(),
      );
    } catch (e) {
      return null;
    }
  }

  /// Find a country by its dial code
  ///
  /// @param dialCode The dial code (e.g., '966', '1', '44')
  /// @return The country object or null if not found
  static Country? findByDialCode(String dialCode) {
    try {
      return countries.firstWhere(
        (country) => country.dialCode == dialCode.replaceAll('+', ''),
      );
    } catch (e) {
      return null;
    }
  }

  /// Get popular countries for quick selection
  ///
  /// Returns a list of commonly selected countries
  ///
  /// @return A list of popular countries
  static List<Country> getPopularCountries() {
    final popularCodes = ['SA', 'AE', 'KW', 'QA', 'BH', 'OM', 'EG', 'JO', 'LB'];
    return popularCodes
        .map((code) => findByCode(code))
        .where((country) => country != null)
        .cast<Country>()
        .toList();
  }

  /// Get all countries sorted by their English names
  ///
  /// @return A list of all countries sorted alphabetically
  static List<Country> getAllCountriesSorted() {
    final sortedCountries = List<Country>.from(countries);
    sortedCountries.sort((a, b) => a.name.compareTo(b.name));
    return sortedCountries;
  }

  /// Get countries by region (if region data is available)
  ///
  /// @param region The region name
  /// @return A list of countries in the specified region
  static List<Country> getCountriesByRegion(String region) {
    // This would need to be implemented based on how regions are defined
    // For now, return all countries
    return countries;
  }

  /// Validate if a country code is valid
  ///
  /// @param code The country code to validate
  /// @return True if the country code exists
  static bool isValidCountryCode(String code) {
    return findByCode(code) != null;
  }

  /// Validate if a dial code is valid
  ///
  /// @param dialCode The dial code to validate
  /// @return True if the dial code exists
  static bool isValidDialCode(String dialCode) {
    return findByDialCode(dialCode) != null;
  }
}
