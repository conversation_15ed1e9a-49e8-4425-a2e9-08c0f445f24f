/// Profile View
///
/// Profile screen showing user information and settings
/// Follows MVVM pattern with Riverpod providers
library profile_view;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/providers/service_providers.dart';
import 'package:towasl/features/profile/presentation/providers/profile_provider.dart';
import 'package:towasl/features/interests/presentation/views/interests_view.dart';
import 'package:towasl/features/location/presentation/views/location_view.dart';
import 'package:towasl/features/personal_info/presentation/views/personal_info_view.dart';
import 'package:towasl/features/core/presentation/providers/app_state_provider.dart';
import 'package:towasl/l10n/app_localizations.dart';

/// Profile View
///
/// Screen for viewing and managing user profile information
class ProfileView extends ConsumerStatefulWidget {
  /// Creates a ProfileView
  const ProfileView({super.key});

  @override
  ConsumerState<ProfileView> createState() => _ProfileViewState();
}

/// State for the ProfileView
class _ProfileViewState extends ConsumerState<ProfileView> {
  @override
  void initState() {
    super.initState();
    // Load user data when the page loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadUserData();
    });
  }

  /// Load user data
  void _loadUserData() {
    final userId = ref.read(userIdProvider);
    if (userId.isNotEmpty) {
      ref.read(profileProvider.notifier).loadUserProfile(userId);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch providers for reactive updates
    final profileState = ref.watch(profileProvider);
    final isLoading = ref.watch(isProfileLoadingProvider);
    final isLoggingOut = ref.watch(isProfileLoggingOutProvider);

    return Scaffold(
      backgroundColor: AppColors.whiteIvory,
      appBar: _buildAppBar(),
      body: SafeArea(
        child: Stack(
          children: [
            // Main content
            isLoading
                ? _buildLoadingState()
                : _buildProfileContent(profileState),

            // Loading overlay for logout
            if (isLoggingOut)
              Container(
                color: Colors.black.withOpacity(0.3),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 16),
                      Text(
                        AppLocalizations.of(context).loggingOut,
                        style: const TextStyle(
                          color: AppColors.whitePure,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Build app bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.primaryPurple,
      elevation: 0,
      title: Text(
        AppLocalizations.of(context).profile,
        style: const TextStyle(
          color: AppColors.whitePure,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      leading: IconButton(
        icon: const Icon(
          Icons.arrow_back,
          color: AppColors.whitePure,
        ),
        onPressed: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  /// Build loading state
  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  /// Build profile content
  Widget _buildProfileContent(ProfileState profileState) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile header
          _buildProfileHeader(),

          const SizedBox(height: 32),

          // Profile sections
          _buildInterestsSection(profileState),
          const SizedBox(height: 16),

          _buildPersonalInfoSection(profileState),
          const SizedBox(height: 16),

          _buildLocationSection(profileState),
          const SizedBox(height: 32),

          // Sign out button
          _buildSignOutButton(),
        ],
      ),
    );
  }

  /// Build profile header
  Widget _buildProfileHeader() {
    return Center(
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: const BoxDecoration(
              color: AppColors.primaryPurple,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.person,
              size: 40,
              color: AppColors.whitePure,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).myProfile,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.greyDark,
            ),
          ),
        ],
      ),
    );
  }

  /// Build interests section
  Widget _buildInterestsSection(ProfileState profileState) {
    return _buildProfileSection(
      title: AppLocalizations.of(context).interests,
      icon: Icons.favorite_outline,
      canEdit: true,
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const InterestsView(isFromSetting: true),
          ),
        );
      },
    );
  }

  /// Build personal info section
  Widget _buildPersonalInfoSection(ProfileState profileState) {
    return _buildProfileSection(
      title: AppLocalizations.of(context).personalInfo,
      icon: Icons.person_outline,
      canEdit: true,
      onTap: () {
        // Get the current user ID with fallback to storage (same pattern as other views)
        String userId = '';

        // First try from userIdProvider
        try {
          userId = ref.read(userIdProvider);
        } catch (e) {
          if (kDebugMode) {
            print(
                'ProfileView: Could not get user ID from userIdProvider - $e');
          }
        }

        // If still empty, try from storage service directly
        if (userId.isEmpty) {
          try {
            final storageService = ref.read(storageServiceProvider);
            userId = storageService.getUserIDValue();
            if (kDebugMode) {
              print('ProfileView: Got user ID from storage: $userId');
            }
          } catch (e) {
            if (kDebugMode) {
              print('ProfileView: Could not get user ID from storage - $e');
            }
          }
        }

        if (kDebugMode) {
          print(
              'ProfileView: Navigating to PersonalInfoView with userId: $userId');
        }

        if (userId.isNotEmpty) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => PersonalInfoView(
                isViewOnly: true,
                userId: userId,
              ),
            ),
          );
        } else {
          if (kDebugMode) {
            print(
                'ProfileView: No userId found, cannot navigate to PersonalInfoView');
          }
        }
      },
    );
  }

  /// Build location section
  Widget _buildLocationSection(ProfileState profileState) {
    return _buildProfileSection(
      title: AppLocalizations.of(context).location,
      icon: Icons.location_on_outlined,
      canEdit: true,
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const LocationView(isFromSetting: true),
          ),
        );
      },
    );
  }

  /// Build profile section widget
  Widget _buildProfileSection({
    required String title,
    String? subtitle,
    required IconData icon,
    required bool canEdit,
    VoidCallback? onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.whitePure,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.greyLight.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: canEdit ? onTap : null,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppColors.primaryPurple.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.primaryPurple,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.greyDark,
                        ),
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.greyMedium,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                if (canEdit)
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: AppColors.greyMedium,
                    size: 16,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build sign out button
  Widget _buildSignOutButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          _showSignOutDialog();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: AppColors.whitePure,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          AppLocalizations.of(context).signOut,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  /// Show sign out confirmation dialog
  void _showSignOutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).signOut),
          content: Text(AppLocalizations.of(context).signOutConfirmation),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(AppLocalizations.of(context).cancel),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                ref.read(profileProvider.notifier).signOut();
              },
              child: Text(
                AppLocalizations.of(context).signOut,
                style: const TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
